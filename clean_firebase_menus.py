#!/usr/bin/env python3
"""
Script to clean Firebase menu data and replace with proper sample data
This script removes all production menu data and populates with sample data using formal naming conventions.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Clean Firebase menu data and replace with sample data"""
    try:
        from firebase_db import initialize_firebase, db
        
        print("🧹 Cleaning Firebase Menu Data...")
        print("=" * 50)
        
        if not initialize_firebase():
            print("❌ Failed to initialize Firebase")
            return False
        
        print("✅ Firebase initialized successfully")
        print()
        
        # Step 1: Clear existing menu data
        print("🗑️  Step 1: Clearing existing menu data...")
        try:
            menus_ref = db.reference("menus")
            menus_ref.delete()
            print("✅ Existing menu data cleared")
        except Exception as e:
            print(f"⚠️  Warning: Could not clear existing menu data: {e}")
        
        print()
        
        # Step 2: Create proper sample menu data structure
        print("📝 Step 2: Creating sample menu data...")
        
        sample_menus_data = {
            "default_menu_items": [
                {"id": 1, "name": "Sample Menu Item 1", "price": 150, "description": "Sample dish 1"},
                {"id": 2, "name": "Sample Menu Item 2", "price": 180, "description": "Sample dish 2"},
                {"id": 3, "name": "Sample Menu Item 3", "price": 200, "description": "Sample dish 3"},
            ],
            "restaurant_menus": {
                "restaurant_1": [
                    {"id": 1, "name": "Sample Menu Item 1", "price": 150, "description": "Delicious sample dish 1"},
                    {"id": 2, "name": "Sample Menu Item 2", "price": 200, "description": "Tasty sample dish 2"},
                    {"id": 3, "name": "Sample Menu Item 3", "price": 180, "description": "Popular sample dish 3"}
                ],
                "restaurant_2": [
                    {"id": 4, "name": "Sample Menu Item 4", "price": 220, "description": "Special sample dish 4"},
                    {"id": 5, "name": "Sample Menu Item 5", "price": 160, "description": "Traditional sample dish 5"},
                    {"id": 6, "name": "Sample Menu Item 6", "price": 190, "description": "Modern sample dish 6"}
                ],
                "restaurant_3": [
                    {"id": 7, "name": "Sample Menu Item 7", "price": 170, "description": "Classic sample dish 7"},
                    {"id": 8, "name": "Sample Menu Item 8", "price": 210, "description": "Premium sample dish 8"}
                ],
                "restaurant_4": [
                    {"id": 9, "name": "Sample Menu Item 9", "price": 140, "description": "Budget sample dish 9"},
                    {"id": 10, "name": "Sample Menu Item 10", "price": 250, "description": "Luxury sample dish 10"}
                ],
                "restaurant_5": [
                    {"id": 11, "name": "Sample Menu Item 11", "price": 195, "description": "Signature sample dish 11"},
                    {"id": 12, "name": "Sample Menu Item 12", "price": 175, "description": "House special sample dish 12"}
                ],
                "restaurant_6": [
                    {"id": 13, "name": "Sample Menu Item 13", "price": 165, "description": "Chef's choice sample dish 13"},
                    {"id": 14, "name": "Sample Menu Item 14", "price": 230, "description": "Gourmet sample dish 14"}
                ]
            }
        }
        
        # Step 3: Upload sample menu data to Firebase
        print("📤 Step 3: Uploading sample menu data to Firebase...")
        try:
            menus_ref = db.reference("menus")
            menus_ref.set(sample_menus_data)
            print("✅ Sample menu data uploaded successfully")
        except Exception as e:
            print(f"❌ Failed to upload sample menu data: {e}")
            return False
        
        print()
        
        # Step 4: Verify the data
        print("🔍 Step 4: Verifying uploaded data...")
        try:
            uploaded_data = menus_ref.get()
            if uploaded_data and "restaurant_menus" in uploaded_data:
                restaurant_menus = uploaded_data["restaurant_menus"]
                print(f"✅ Verified: {len(restaurant_menus)} restaurants with menus")
                
                for restaurant_key, menu_items in restaurant_menus.items():
                    print(f"   - {restaurant_key}: {len(menu_items)} items")
                    if menu_items:
                        first_item = menu_items[0]
                        print(f"     First item: {first_item['name']} - {first_item['price']} ETB")
            else:
                print("❌ Verification failed: No restaurant_menus found")
                return False
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False
        
        print()
        print("🎉 SUCCESS! Firebase menu data has been cleaned and populated with sample data.")
        print()
        print("📊 Sample Menu Data Summary:")
        print("- restaurant_1: Sample Menu Item 1-3 (150-200 ETB)")
        print("- restaurant_2: Sample Menu Item 4-6 (160-220 ETB)")
        print("- restaurant_3: Sample Menu Item 7-8 (170-210 ETB)")
        print("- restaurant_4: Sample Menu Item 9-10 (140-250 ETB)")
        print("- restaurant_5: Sample Menu Item 11-12 (175-195 ETB)")
        print("- restaurant_6: Sample Menu Item 13-14 (165-230 ETB)")
        print()
        print("✅ All restaurants now have proper sample menu data!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning Firebase menu data: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
